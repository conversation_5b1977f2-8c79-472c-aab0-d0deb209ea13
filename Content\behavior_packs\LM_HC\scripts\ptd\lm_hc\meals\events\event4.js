import { BlockTypes, system } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';
import { getRandomLocation } from '../../utilities/vector3';
/**
 * Event 4. Anvil Shower: Creates a rain of anvils around the player for 10 seconds.
 * Anvils are placed as blocks above the player which naturally fall due to gravity.
 * The event runs for exactly 10 seconds with anvils spawning every 5 ticks.
 *
 * @param player - The Player instance for whom to spawn the anvil rain
 * @remarks
 * - Places anvil blocks at random positions above the player
 * - Each anvil is accompanied by an explosion particle effect
 * - Runs for exactly 10 seconds (200 ticks)
 * - Anvils spawn every 5 ticks
 *
 * @throws Logs a warning to console if the event execution fails
 */
export function event4(player) {
    try {
        // Get block type upfront and validate
        const anvil = BlockTypes.get('minecraft:anvil');
        if (!anvil) {
            throw new Error('Failed to get anvil block type');
        }
        const CONFIG = {
            DURATION_TICKS: 100, // 5 seconds * 20 ticks/second (reduced from 10 seconds)
            SPAWN_INTERVAL: 3, // More frequent spawns (reduced from 5)
            XZ_OFFSET_RANGE: [-25, 25], // Wider area coverage (increased from [-16, 16])
            Y_OFFSET_RANGE: [15, 30] // Significantly higher (increased from [4, 16])
        };
        // List of blocks that can be replaced
        const REPLACEABLE_BLOCKS = new Set([
            // Basic vegetation
            'minecraft:air',
            'minecraft:tallgrass',
            'minecraft:short_grass',
            'minecraft:grass',
            'minecraft:fern',
            'minecraft:deadbush',
            'minecraft:seagrass',
            'minecraft:tall_seagrass',
            'minecraft:moss_carpet',
            // Flowers
            'minecraft:red_flower',
            'minecraft:yellow_flower',
            'minecraft:wither_rose',
            'minecraft:cornflower',
            'minecraft:lily_of_the_valley',
            'minecraft:sweet_berry_bush',
            'minecraft:tulip',
            'minecraft:allium',
            'minecraft:azure_bluet',
            'minecraft:blue_orchid',
            'minecraft:dandelion',
            'minecraft:oxeye_daisy',
            'minecraft:poppy',
            // Tall plants
            'minecraft:double_plant',
            'minecraft:lilac',
            'minecraft:peony',
            'minecraft:rose_bush',
            'minecraft:sunflower',
            'minecraft:tall_grass',
            'minecraft:large_fern',
            // Vines and climbing plants
            'minecraft:vine',
            'minecraft:twisting_vines',
            'minecraft:weeping_vines',
            'minecraft:cave_vines',
            // Leaves
            'minecraft:azalea_leaves',
            'minecraft:flowering_azalea_leaves',
            'minecraft:leaves',
            'minecraft:leaves2',
            'minecraft:mangrove_leaves',
            'minecraft:cherry_leaves',
            // Crops and small plants
            'minecraft:wheat',
            'minecraft:carrots',
            'minecraft:potatoes',
            'minecraft:beetroot',
            'minecraft:nether_wart',
            'minecraft:sweet_berries',
            'minecraft:torchflower',
            'minecraft:melon_stem',
            'minecraft:pumpkin_stem'
        ]);
        let tickCount = 0;
        const runId = system.runInterval(() => {
            if (tickCount >= CONFIG.DURATION_TICKS) {
                system.clearRun(runId);
                return;
            }
            const startingLoc = player.getHeadLocation();
            const randomXZOffset = getRandomInt(CONFIG.XZ_OFFSET_RANGE[0], CONFIG.XZ_OFFSET_RANGE[1]);
            const randomYOffset = getRandomInt(CONFIG.Y_OFFSET_RANGE[0], CONFIG.Y_OFFSET_RANGE[1]);
            const location = getRandomLocation(startingLoc, player.dimension, 0, randomXZOffset, randomYOffset, true);
            if (location) {
                const spawnLoc = {
                    x: Math.floor(location.x),
                    y: Math.floor(location.y),
                    z: Math.floor(location.z)
                };
                // Check if we can safely place the anvil
                const block = player.dimension.getBlock(spawnLoc);
                if (block && block.typeId && REPLACEABLE_BLOCKS.has(block.typeId)) {
                    block.setType(anvil);
                    // Enhanced effects
                    player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', location);
                    player.runCommand(`playsound random.anvil_land @s ${spawnLoc.x} ${spawnLoc.y} ${spawnLoc.z} 1 0.8`);
                }
            }
            tickCount += CONFIG.SPAWN_INTERVAL;
        }, CONFIG.SPAWN_INTERVAL);
    }
    catch (error) {
        console.warn(`Failed to execute anvil shower event: ${error}`);
    }
    return;
}
