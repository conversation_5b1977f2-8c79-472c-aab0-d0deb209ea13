{"format_version": "1.8.0", "minecraft:client_entity": {"description": {"identifier": "ptd_lmhc:hostile_enderman", "min_engine_version": "1.8.0", "materials": {"default": "enderman", "invisible": "enderman_invisible"}, "textures": {"default": "textures/entity/enderman/enderman"}, "geometry": {"default": "geometry.ptd_lmhc.enderman.v1.8"}, "scripts": {"pre_animation": ["variable.tcos0 = (Math.cos(query.modified_distance_moved * 38.17) * query.modified_move_speed / variable.gliding_speed_value) * 28.65;"]}, "animations": {"look_at_target_default": "animation.humanoid.look_at_target.default", "look_at_target_gliding": "animation.humanoid.look_at_target.gliding", "look_at_target_swimming": "animation.humanoid.look_at_target.swimming", "move": "animation.humanoid.move", "attack.rotations": "animation.humanoid.attack.rotations", "bob": "animation.humanoid.bob", "base_pose": "animation.enderman.base_pose", "arms_legs": "animation.enderman.arms_legs", "carrying": "animation.enderman.carrying", "scary_face": "animation.enderman.scary_face"}, "animation_controllers": [{"look_at_target": "controller.animation.humanoid.look_at_target"}, {"move": "controller.animation.humanoid.move"}, {"attack": "controller.animation.humanoid.attack"}, {"bob": "controller.animation.humanoid.bob"}, {"base_pose": "controller.animation.enderman.base_pose"}, {"carrying": "controller.animation.enderman.carrying"}, {"scary_face": "controller.animation.enderman.scary_face"}], "render_controllers": ["controller.render.enderman"], "spawn_egg": {"texture": "spawn_egg", "texture_index": 7}}}}