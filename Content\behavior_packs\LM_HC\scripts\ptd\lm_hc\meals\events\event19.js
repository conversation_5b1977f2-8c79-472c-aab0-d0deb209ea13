import { EntityComponentTypes } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { spawnEntitiesWithInterval } from '../../utilities/summonEntity';
/**
 * Event 19: Pet Army
 * Spawns 10 tamed wolves and 5 tamed cats that will protect and follow the player
 * @param player The player who will own the pets
 */
export function event19(player) {
    try {
        // Configure the entity types and quantities to spawn
        const entityConfigs = [
            { entityId: 'minecraft:wolf', count: 10 },
            { entityId: 'minecraft:cat', count: 5 }
        ];
        // Create a location callback function that returns a random position around the player
        const getSpawnLocation = () => {
            const spawnPos = getRandomLocation(player.location, player.dimension, 2, 3, 0, true);
            // Play particle effect at spawn location if position is valid
            if (spawnPos) {
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
                player.dimension.playSound('mob.endermen.portal', spawnPos);
            }
            else {
                console.warn('Failed to find safe spawn location for pet');
            }
            return spawnPos;
        };
        // Define a callback to be executed when an entity is spawned to tame it
        const onPetSpawned = (pet) => {
            try {
                // Get tameable component and tame the pet
                const tameable = pet.getComponent(EntityComponentTypes.Tameable);
                if (tameable && !tameable.isTamed) {
                    // Set the pet as tamed and owned by the player
                    tameable.tame(player);
                }
            }
            catch (error) {
                console.warn(`Failed to tame pet: ${error}`);
            }
        };
        // Start spawning entities with a 2 tick delay between each
        spawnEntitiesWithInterval(player.dimension, entityConfigs, getSpawnLocation, 2, onPetSpawned).catch((error) => {
            console.warn(`Error in event19 spawning process: ${error}`);
        });
    }
    catch (error) {
        console.warn(`Failed to execute event 19: ${error}`);
    }
    return;
}
