/**
 * Constants for the "Sluggish" event
 */
const EFFECT_DURATION = 15 * 20; // 15 seconds in ticks
const EFFECT_AMPLIFIER = 3; // Slowness level IV (strong slowness)
/**
 * Event 74: Sluggish - Player is affected by strong slowness for 15 seconds
 * Applies a high-level slowness effect to the player, severely reducing movement speed
 *
 * @param player - The player who triggered the event
 */
export function event74(player) {
    try {
        // Apply strong slowness effect to the player
        player.addEffect('slowness', EFFECT_DURATION, {
            amplifier: EFFECT_AMPLIFIER,
            showParticles: true
        });
    }
    catch (error) {
        console.warn(`Failed to execute event 74: ${error}`);
    }
    return;
}
