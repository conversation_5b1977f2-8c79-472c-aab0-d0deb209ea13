import { system } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
/**
 * Event 18: <PERSON> the Overpowered Zombie
 * Spawns a zombie named <PERSON> with enchanted Netherite gear
 * @param player The player to spawn Bob near
 */
export function event18(player) {
    try {
        let spawnPos = getRandomLocation(player.location, player.dimension, 5, 4, 1, true);
        // Find solid block below and adjust position
        if (spawnPos) {
            // Check blocks below until we find a solid one
            let checkPos = { ...spawnPos };
            for (let i = 0; i < 5; i++) {
                checkPos.y--;
                const block = player.dimension.getBlock(checkPos);
                if (block && !block.isAir) {
                    // Found solid block, adjust spawn position to be 1.1 blocks above it
                    spawnPos.y = checkPos.y + 1.1;
                    break;
                }
            }
            // Spawn the zombie
            const zombie = player.dimension.spawnEntity('ptd_lmhc:zombie', spawnPos);
            player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
            player.dimension.playSound('mob.endermen.portal', spawnPos);
            // Name it Bob
            zombie.nameTag = 'Bob';
            // Add equipment via event
            system.runTimeout(() => {
                zombie.triggerEvent('ptd_lmhc:as_bob');
            }, 1);
        }
        else {
            console.warn('Failed to find a valid spawn position for Bob.');
        }
    }
    catch (error) {
        console.warn(`Failed to execute event 18: ${error}`);
    }
}
