/**
 * Event 36: <PERSON> Guardian's Wrath
 * Instantly afflicted with Mining Fatigue III for 1 minute
 *
 * @param player - The player to afflict with Mining Fatigue
 */
export function event36(player) {
    try {
        // Apply Mining Fatigue III for 1 minute (1200 ticks = 1 minute)
        const effectOptions = {
            amplifier: 2, // Level 3 = amplifier 2
            showParticles: true
        };
        player.addEffect('mining_fatigue', 1200, effectOptions);
    }
    catch (error) {
        console.warn(`Failed to execute event 36: ${error}`);
    }
    return;
}
