import { ItemStack, system } from '@minecraft/server';
import { getRandomInt } from '../../utilities/rng';
import { getRandomLocation } from '../../utilities/vector3';
// Dynamic property name to track gold blocks
const GOLD_BLOCKS_PROPERTY = 'piglinBankGoldBlocks';
// Total number of gold blocks to drop
const TOTAL_GOLD_BLOCKS = 10;
/**
 * Event 39. Piglin Bank: A piglin brute spawns and drops 10 gold blocks.
 * The gold blocks are spawned in a fountain-like pattern with random impulses.
 * After all gold blocks have been spawned, the brute is despawned.
 *
 * @param player - The player to spawn the piglin brute near
 */
export function event39(player) {
    try {
        // Get player's dimension
        const dimension = player.dimension;
        // Find a suitable spawn location for the piglin brute
        const spawnPos = getRandomLocation(player.location, dimension, 8, 4, 0, true);
        if (!spawnPos) {
            console.warn('Failed to find suitable spawn location for piglin brute');
            return;
        }
        // Spawn the piglin brute
        const piglinBrute = dimension.spawnEntity('minecraft:piglin_brute', spawnPos);
        if (!piglinBrute) {
            return;
        }
        // Set dynamic property to track gold blocks
        piglinBrute.setDynamicProperty(GOLD_BLOCKS_PROPERTY, 0);
        // Create an interval to drop gold blocks
        let droppedBlocks = 0;
        const intervalId = system.runInterval(() => {
            try {
                // Check if the entity still exists
                if (!piglinBrute) {
                    system.clearRun(intervalId);
                    return;
                }
                // If we've dropped all blocks, remove the piglin brute and clear the interval
                if (droppedBlocks >= TOTAL_GOLD_BLOCKS) {
                    dimension.spawnParticle('minecraft:cauldron_explosion_emitter', piglinBrute.location);
                    dimension.playSound('mob.endermen.portal', piglinBrute.location);
                    piglinBrute.remove();
                    system.clearRun(intervalId);
                    return;
                }
                // Create a gold block item stack
                const goldBlock = new ItemStack('minecraft:gold_block', 1);
                // Spawn the gold block at the piglin brute's location
                const item = piglinBrute.dimension.spawnItem(goldBlock, piglinBrute.location);
                // Apply random impulses to create a fountain effect
                if (item) {
                    // Upward impulse (always positive y)
                    const upwardForce = 0.3 + Math.random() * 0.2;
                    // Random horizontal impulses
                    const xForce = Math.random() * 0.4 - 0.2;
                    const zForce = Math.random() * 0.4 - 0.2;
                    // Apply the impulse
                    item.applyImpulse({ x: xForce, y: upwardForce, z: zForce });
                }
                // Update the count of dropped gold blocks
                droppedBlocks++;
                piglinBrute.setDynamicProperty(GOLD_BLOCKS_PROPERTY, droppedBlocks);
            }
            catch (error) {
                console.warn(`Error in gold block drop: ${error}`);
                // If there's an error, try to clean up the piglin brute and clear the interval
                if (piglinBrute && piglinBrute) {
                    piglinBrute.remove();
                }
                system.clearRun(intervalId);
            }
        }, getRandomInt(2, 8)); // Random interval between 2-8 ticks
    }
    catch (error) {
        console.warn(`Failed to execute event 39: ${error}`);
    }
    return;
}
