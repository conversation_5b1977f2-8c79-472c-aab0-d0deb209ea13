import { system } from '@minecraft/server';
import { getRandomLocation } from '../../utilities/vector3';
import { getRandomInt } from '../../utilities/rng';
/**
 * Event 32. Piglin Invasion: Spawns 30 piglin brutes that start attacking the player
 * @param player The player who triggered the event
 */
export function event32(player) {
    try {
        const piglinCount = 30;
        let spawned = 0;
        const spawnInterval = system.runInterval(() => {
            if (spawned >= piglinCount) {
                system.clearRun(spawnInterval);
                return;
            }
            const location = getRandomLocation(player.location, player.dimension, 1, // base offset
            getRandomInt(-6, 6), // additional offset
            getRandomInt(0, 2), // Y offset
            true // check for air block
            );
            if (location) {
                try {
                    player.dimension.spawnEntity('minecraft:piglin_brute', location);
                    player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', location);
                    player.dimension.playSound('mob.endermen.portal', location);
                }
                catch (error) {
                    console.warn(`Failed to spawn piglin brute: ${error}`);
                }
            }
            spawned++;
        }, 5);
    }
    catch (error) {
        console.warn(`Failed to execute event 32: ${error}`);
    }
    return;
}
