import { updateAchievements } from './achievement';
import { event1 } from './events/event1';
import { event2 } from './events/event2';
import { event3 } from './events/event3';
import { event4 } from './events/event4';
import { event5 } from './events/event5';
import { event6 } from './events/event6';
import { event7 } from './events/event7';
import { event8 } from './events/event8';
import { event9 } from './events/event9';
import { event10 } from './events/event10';
import { event11 } from './events/event11';
import { event12 } from './events/event12';
import { event13 } from './events/event13';
import { event14 } from './events/event14';
import { event15 } from './events/event15';
import { event16 } from './events/event16';
import { event17 } from './events/event17';
import { event18 } from './events/event18';
import { event19 } from './events/event19';
import { event20 } from './events/event20';
import { event21 } from './events/event21';
import { event22 } from './events/event22';
import { event23 } from './events/event23';
import { event24 } from './events/event24';
import { event25 } from './events/event25';
import { event26 } from './events/event26';
import { event27 } from './events/event27';
import { event28 } from './events/event28';
import { event29 } from './events/event29';
import { event30 } from './events/event30';
import { event31 } from './events/event31';
import { event32 } from './events/event32';
import { event33 } from './events/event33';
import { event34 } from './events/event34';
import { event35 } from './events/event35';
import { event36 } from './events/event36';
import { event37 } from './events/event37';
import { event38 } from './events/event38';
import { event39 } from './events/event39';
import { event40 } from './events/event40';
import { event41 } from './events/event41';
import { event42 } from './events/event42';
import { event43 } from './events/event43';
import { event44 } from './events/event44';
import { event45 } from './events/event45';
import { event46 } from './events/event46';
import { event47 } from './events/event47';
import { event48 } from './events/event48';
import { event49 } from './events/event49';
import { event50 } from './events/event50';
import { event51 } from './events/event51';
import { event52 } from './events/event52';
import { event53 } from './events/event53';
import { event54 } from './events/event54';
import { event55 } from './events/event55';
import { event56 } from './events/event56';
import { event57 } from './events/event57';
import { event58 } from './events/event58';
import { event59 } from './events/event59';
import { event60 } from './events/event60';
import { event61 } from './events/event61';
import { event62 } from './events/event62';
import { event63 } from './events/event63';
import { event64 } from './events/event64';
import { event65 } from './events/event65';
import { event66 } from './events/event66';
import { event67 } from './events/event67';
import { event68 } from './events/event68';
import { event69 } from './events/event69';
import { event70 } from './events/event70';
import { event71 } from './events/event71';
import { event72 } from './events/event72';
import { event73 } from './events/event73';
import { event74 } from './events/event74';
import { event75 } from './events/event75';
// Removed events 61-75 imports
/**
 * Set of custom meal identifiers that trigger random events when consumed
 * Each meal in this set will activate the onEatMeal function when eaten
 */
export const meals = new Set([
    'ptd_lmhc:abalone',
    'ptd_lmhc:banana_waffle',
    'ptd_lmhc:bananadog',
    'ptd_lmhc:beef_stew_rice',
    'ptd_lmhc:bibimbap',
    'ptd_lmhc:blueberry_waffle',
    'ptd_lmhc:bonbon_bowl',
    'ptd_lmhc:brain_stew',
    'ptd_lmhc:cheese_shell_soup',
    'ptd_lmhc:cookie_waffle',
    'ptd_lmhc:cookiekiwi_waffle',
    'ptd_lmhc:cream_cheese_waffle',
    'ptd_lmhc:crusty_cheese_cup',
    'ptd_lmhc:curryrice',
    'ptd_lmhc:dumpling_soup',
    'ptd_lmhc:eggbacon',
    'ptd_lmhc:fish_dish',
    'ptd_lmhc:fish_taco',
    'ptd_lmhc:flavorboom_waffle',
    'ptd_lmhc:freshcream_waffle',
    'ptd_lmhc:fried_chicken',
    'ptd_lmhc:fried_squid',
    'ptd_lmhc:fruitcream_sandwiches',
    'ptd_lmhc:fruits_waffle',
    'ptd_lmhc:fudge_pop',
    'ptd_lmhc:delicious_rice',
    'ptd_lmhc:grape_waffle',
    'ptd_lmhc:iatethis',
    'ptd_lmhc:ice_cream_waffle',
    'ptd_lmhc:jar_of_pickles',
    'ptd_lmhc:jelly_cake',
    'ptd_lmhc:jjajangmyeon',
    'ptd_lmhc:kimbap_sushi',
    'ptd_lmhc:limesicle_pop',
    'ptd_lmhc:meal_worms',
    'ptd_lmhc:octopus_sandwich',
    'ptd_lmhc:omurice',
    'ptd_lmhc:onigiri',
    'ptd_lmhc:orange_chicken',
    'ptd_lmhc:orange_drizzled_ham',
    'ptd_lmhc:pancakes',
    'ptd_lmhc:pizza',
    'ptd_lmhc:prawnburger_waffle',
    'ptd_lmhc:ramen_galore',
    'ptd_lmhc:raspberry_cream_cake',
    'ptd_lmhc:rotten_food',
    'ptd_lmhc:sashimi',
    'ptd_lmhc:shrimp_partae',
    'ptd_lmhc:shrimpfry',
    'ptd_lmhc:silky_salmon',
    'ptd_lmhc:skewered_selections',
    'ptd_lmhc:soup_of_slop',
    'ptd_lmhc:spaghetti',
    'ptd_lmhc:split_pea_soup',
    'ptd_lmhc:steak',
    'ptd_lmhc:steamed_claw',
    'ptd_lmhc:steamed_variety',
    'ptd_lmhc:strawberry_cream_cake',
    'ptd_lmhc:strawberry_waffle',
    'ptd_lmhc:striped_sandwich',
    'ptd_lmhc:sushi_template',
    'ptd_lmhc:sweet_waffle',
    'ptd_lmhc:talkin_turkey',
    'ptd_lmhc:teriyaki',
    'ptd_lmhc:toasted_cheese_muffin',
    'ptd_lmhc:toppoki',
    'ptd_lmhc:triple_nougat_cream',
    'ptd_lmhc:tripleleche_cake',
    'ptd_lmhc:tropical_delight',
    'ptd_lmhc:truloves_first_bite',
    'ptd_lmhc:turkish_delights',
    'ptd_lmhc:vegetable_waffle',
    'ptd_lmhc:waffle',
    'ptd_lmhc:waffle_cake',
    'ptd_lmhc:wrapped_spinach_chicken'
]);
/**
 * Handles the random event selection and execution when a player consumes a lucky meal
 * Randomly selects one of 75 possible events and executes it for the player
 * Each event has a unique effect and displays a corresponding message to the player
 *
 * Some events create mystery items with unpredictable effects:
 * - Event 23: Lucky Elixir (positive effects)
 * - Event 44: Unlucky Elixir (negative effects)
 * Both elixirs appear identical to create an element of surprise.
 *
 * @param player - The Player entity that consumed the meal
 * @throws Will log a warning to console if event execution fails
 * @returns void
 */
export function onEatMeal(player) {
    // Generate a random number between 1 and 75 to select an event
    const event = getRandomIntMeals(1, 75);
    try {
        // Execute the selected event and display its corresponding message
        // Each case represents a unique event with different gameplay effects
        switch (event) {
            case 1:
                event1(player);
                updateAchievements(player, 1, true);
                player.sendMessage('Event §21§r: Treasure Trove');
                break;
            case 2:
                event2(player);
                updateAchievements(player, 2, true);
                player.sendMessage('Event §22§r: TNT Rain');
                break;
            case 3:
                event3(player);
                updateAchievements(player, 3, true);
                player.sendMessage("Event §23§r: Bunch O' Diamonds");
                break;
            case 4:
                event4(player);
                updateAchievements(player, 4, true);
                player.sendMessage('Event §24§r: Anvil Shower');
                break;
            case 5:
                event5(player);
                updateAchievements(player, 5, true);
                player.sendMessage("Event §25§r: Dragon's Hoard");
                break;
            case 6:
                event6(player);
                updateAchievements(player, 6, true);
                player.sendMessage('Event §26§r: Supercharged Creeper');
                break;
            case 7:
                event7(player);
                updateAchievements(player, 7, true);
                player.sendMessage('Event §27§r: Mystic Trader');
                break;
            case 8:
                event8(player);
                updateAchievements(player, 8, true);
                player.sendMessage('Event §28§r: Wither Summon');
                break;
            case 9:
                event9(player);
                updateAchievements(player, 9, true);
                player.sendMessage('Event §29§r: Totems of Undying');
                break;
            case 10:
                event10(player);
                updateAchievements(player, 10, true);
                player.sendMessage('Event §210§r: Zombie Swarm');
                break;
            case 11:
                event11(player);
                updateAchievements(player, 11, true);
                player.sendMessage('Event §211§r: Potions Galore');
                break;
            case 12:
                event12(player);
                updateAchievements(player, 12, true);
                player.sendMessage('Event §212§r: Lava Pool');
                break;
            case 13:
                event13(player);
                updateAchievements(player, 13, true);
                player.sendMessage('Event §213§r: Enchanted Arsenal');
                break;
            case 14:
                event14(player);
                updateAchievements(player, 14, true);
                player.sendMessage('Event §214§r: Blindness Curse');
                break;
            case 15:
                event15(player);
                updateAchievements(player, 15, true);
                player.sendMessage('Event §215§r: Wither Slayer');
                break;
            case 16:
                event16(player);
                updateAchievements(player, 16, true);
                player.sendMessage('Event §216§r: Teleport to the End');
                break;
            case 17:
                event17(player);
                updateAchievements(player, 17, true);
                player.sendMessage('Event §217§r: Wishing Well');
                break;
            case 18:
                event18(player);
                updateAchievements(player, 18, true);
                player.sendMessage('Event §218§r: Bob the Zombie');
                break;
            case 19:
                event19(player);
                updateAchievements(player, 19, true);
                player.sendMessage('Event §219§r: Pet Army');
                break;
            case 20:
                event20(player);
                updateAchievements(player, 20, true);
                player.sendMessage('Event §220§r: Instant Hunger');
                break;
            case 21:
                event21(player);
                updateAchievements(player, 21, true);
                player.sendMessage('Event §221§r: Super Speed Boots');
                break;
            case 22:
                event22(player);
                updateAchievements(player, 22, true);
                player.sendMessage('Event §222§r: Shulker Hell');
                break;
            case 23:
                event23(player);
                updateAchievements(player, 23, true);
                player.sendMessage('Event §223§r: Lucky Elixir');
                break;
            case 24:
                event24(player);
                updateAchievements(player, 24, true);
                player.sendMessage('Event §224§r: Sky Drop');
                break;
            case 25:
                event25(player);
                updateAchievements(player, 25, true);
                player.sendMessage('Event §225§r: Golem Guardian');
                break;
            case 26:
                event26(player);
                updateAchievements(player, 26, true);
                player.sendMessage('Event §226§r: Exploding Blocks');
                break;
            case 27:
                event27(player);
                updateAchievements(player, 27, true);
                player.sendMessage('Event §227§r: Mining Away');
                break;
            case 28:
                event28(player);
                updateAchievements(player, 28, true);
                player.sendMessage('Event §228§r: Cursed Pickaxe');
                break;
            case 29:
                event29(player);
                updateAchievements(player, 29, true);
                player.sendMessage('Event §229§r: Flying Mode');
                break;
            case 30:
                event30(player);
                updateAchievements(player, 30, true);
                player.sendMessage('Event §230§r: Poisonous Feast');
                break;
            case 31:
                event31(player);
                updateAchievements(player, 31, true);
                player.sendMessage('Event §231§r: Emerald Rain');
                break;
            case 32:
                event32(player);
                updateAchievements(player, 32, true);
                player.sendMessage('Event §232§r: Piglin Invasion');
                break;
            case 33:
                event33(player);
                updateAchievements(player, 33, true);
                player.sendMessage('Event §233§r: Diamond Meteor');
                break;
            case 34:
                event34(player);
                updateAchievements(player, 34, true);
                player.sendMessage('Event §234§r: Obsidian Cage');
                break;
            case 35:
                event35(player);
                updateAchievements(player, 35, true);
                player.sendMessage('Event §235§r: XP Bonanza');
                break;
            case 36:
                event36(player);
                updateAchievements(player, 36, true);
                player.sendMessage("Event §236§r: Elder Guardian's Wrath");
                break;
            case 37:
                event37(player);
                updateAchievements(player, 37, true);
                player.sendMessage('Event §237§r: OP Fishing Rod');
                break;
            case 38:
                player.sendMessage('Event §238§r: Hotbar Vanish');
                event38(player);
                updateAchievements(player, 38, true);
                break;
            case 39:
                player.sendMessage('Event §239§r: Piglin Bank');
                event39(player);
                updateAchievements(player, 39, true);
                break;
            case 40:
                player.sendMessage('Event §240§r: Hostile Endermen');
                event40(player);
                updateAchievements(player, 40, true);
                break;
            case 41:
                event41(player);
                updateAchievements(player, 41, true);
                player.sendMessage('Event §241§r: Instant House');
                break;
            case 42:
                event42(player);
                updateAchievements(player, 42, true);
                player.sendMessage('Event §242§r: Mega Ravager');
                break;
            case 43:
                event43(player);
                updateAchievements(player, 43, true);
                player.sendMessage('Event §243§r: Lucky Cow');
                break;
            case 44:
                event44(player);
                updateAchievements(player, 44, true);
                player.sendMessage('Event §244§r: Unlucky Elixir');
                break;
            case 45:
                event45(player);
                updateAchievements(player, 45, true);
                player.sendMessage('Event §245§r: Super Elytra');
                break;
            case 46:
                event46(player);
                updateAchievements(player, 46, true);
                player.sendMessage('Event §246§r: Swarm of Bees');
                break;
            case 47:
                event47(player);
                updateAchievements(player, 47, true);
                player.sendMessage('Event §247§r: Mob Convert');
                break;
            case 48:
                event48(player);
                updateAchievements(player, 48, true);
                player.sendMessage('Event §248§r: Lightning Storm');
                break;
            case 49:
                event49(player);
                updateAchievements(player, 49, true);
                player.sendMessage('Event §249§r: Lucky Horse');
                break;
            case 50:
                event50(player);
                updateAchievements(player, 50, true);
                player.sendMessage('Event §250§r: Detonation');
                break;
            case 51:
                event51(player);
                updateAchievements(player, 51, true);
                player.sendMessage('Event §251§r: Shulker Box Jackpot');
                break;
            case 52:
                event52(player);
                updateAchievements(player, 52, true);
                player.sendMessage('Event §252§r: Silverfish Invasion');
                break;
            case 53:
                event53(player);
                updateAchievements(player, 53, true);
                player.sendMessage('Event §253§r: Beacon of Hope');
                break;
            case 54:
                event54(player);
                updateAchievements(player, 54, true);
                player.sendMessage('Event §254§r: Sudden Night');
                break;
            case 55:
                event55(player);
                updateAchievements(player, 55, true);
                player.sendMessage('Event §255§r: Lucky Lava');
                break;
            case 56:
                event56(player);
                updateAchievements(player, 56, true);
                player.sendMessage("Event §256§r: Fake O' Diamonds");
                break;
            case 57:
                event57(player);
                updateAchievements(player, 57, true);
                player.sendMessage('Event §257§r: Mega Villager');
                break;
            case 58:
                event58(player);
                updateAchievements(player, 58, true);
                player.sendMessage('Event §258§r: Item Vanish');
                break;
            case 59:
                event59(player);
                updateAchievements(player, 59, true);
                player.sendMessage('Event §259§r: Mob Smite');
                break;
            case 60:
                event60(player);
                updateAchievements(player, 60, true);
                player.sendMessage('Event §260§r: Mob Horde');
                break;
            case 61:
                event61(player);
                updateAchievements(player, 61, true);
                player.sendMessage('Event §261§r: Sonic Boom');
                break;
            case 62:
                event62(player);
                updateAchievements(player, 62, true);
                player.sendMessage('Event §262§r: Teleport Confusion');
                break;
            case 63:
                event63(player);
                updateAchievements(player, 63, true);
                player.sendMessage('Event §263§r: Sheep Party');
                break;
            case 64:
                event64(player);
                updateAchievements(player, 64, true);
                player.sendMessage("Event §264§r: Johnny's Here");
                break;
            case 65:
                event65(player);
                updateAchievements(player, 65, true);
                player.sendMessage('Event §265§r: Cake Chamber');
                break;
            case 66:
                event66(player);
                updateAchievements(player, 66, true);
                player.sendMessage('Event §266§r: Arrow Barrage');
                break;
            case 67:
                event67(player);
                updateAchievements(player, 67, true);
                player.sendMessage('Event §267§r: Dinnerbone Party');
                break;
            case 68:
                event68(player);
                updateAchievements(player, 68, true);
                player.sendMessage('Event §268§r: Cobweb Cage');
                break;
            case 69:
                event69(player);
                updateAchievements(player, 69, true);
                player.sendMessage('Event §269§r: Ascension');
                break;
            case 70:
                event70(player);
                updateAchievements(player, 70, true);
                player.sendMessage("Event §270§r: It's a bit Breezy");
                break;
            case 71:
                event71(player);
                updateAchievements(player, 71, true);
                player.sendMessage('Event §271§r: Begone, Monsters!');
                break;
            case 72:
                event72(player);
                updateAchievements(player, 72, true);
                player.sendMessage("Event §272§r: Wither's Breath");
                break;
            case 73:
                event73(player);
                updateAchievements(player, 73, true);
                player.sendMessage('Event §273§r: Divine Escape');
                break;
            case 74:
                event74(player);
                updateAchievements(player, 74, true);
                player.sendMessage('Event §274§r: Sluggish');
                break;
            case 75:
                event75(player);
                updateAchievements(player, 75, true);
                player.sendMessage('Event §275§r: Treasure King');
                break;
        }
    }
    catch (error) {
        // Log any errors that occur during event execution
        console.warn(`Error in onEatMeal: ${error}`);
    }
    return;
}
/**
 * Generates a random integer between min and max (inclusive)
 * @param min The minimum value (inclusive)
 * @param max The maximum value (inclusive)
 * @returns A random integer between min and max
 * @throws Error if min is greater than max
 */
function getRandomIntMeals(min, max) {
    // Ensure parameters are integers
    min = Math.ceil(min);
    max = Math.floor(max);
    // Validate input
    if (min > max) {
        console.warn(`getRandomInt: min (${min}) is greater than max (${max})`);
    }
    // Generate a completely random number between min and max (inclusive)
    return Math.floor(Math.random() * (max - min + 1)) + min;
}
