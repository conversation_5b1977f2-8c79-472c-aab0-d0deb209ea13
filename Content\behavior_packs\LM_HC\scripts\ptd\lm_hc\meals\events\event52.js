import { getRandomLocation } from '../../utilities/vector3';
import { spawnEntitiesWithInterval } from '../../utilities/summonEntity';
/**
 * Event52: Silverfish Invasion
 * Spawns 50 silverfish around the player
 * @param player The player who triggered the event
 */
export function event52(player) {
    try {
        // Configure the entity type and quantity to spawn
        const entityConfigs = [{ entityId: 'minecraft:silverfish', count: 50 }];
        // Create a location callback function that returns a random position around the player
        const getSpawnLocation = () => {
            const spawnPos = getRandomLocation(player.location, player.dimension, 3, // minDistance
            5, // additionalOffset (maxDistance = 8)
            0, true);
            // Play particle effect at spawn location if position is valid
            if (spawnPos) {
                player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnPos);
                player.dimension.playSound('mob.endermen.portal', spawnPos);
            }
            return spawnPos;
        };
        // Start spawning silverfish with a short delay between each
        spawnEntitiesWithInterval(player.dimension, entityConfigs, getSpawnLocation, 2 // 2 ticks = 0.1 seconds between spawns
        ).catch((error) => {
            console.warn(`Error in event52 spawning process: ${error}`);
        });
    }
    catch (error) {
        console.warn(`Failed to execute event 52: ${error}`);
    }
    return;
}
