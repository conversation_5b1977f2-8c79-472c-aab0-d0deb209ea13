import { system, BlockTypes } from '@minecraft/server';
/**
 * Event 51: Shulker Box Jackpot
 * Spawns a colored shulker box filled with random rare loot in front of the player
 * The box breaks itself after 1 tick for dramatic effect
 * @param player The player who triggered the event
 */
export async function event51(player) {
    try {
        // Get player's view direction and round to nearest cardinal direction
        const viewDir = player.getViewDirection();
        const preferredDirection = {
            x: Math.abs(viewDir.x) > Math.abs(viewDir.z) ? Math.sign(viewDir.x) : 0,
            y: 0,
            z: Math.abs(viewDir.z) > Math.abs(viewDir.x) ? Math.sign(viewDir.z) : 0
        };
        // List of all shulker box types from protectedBlocks.ts
        const shulkerBoxTypes = [
            'minecraft:undyed_shulker_box',
            'minecraft:white_shulker_box',
            'minecraft:orange_shulker_box',
            'minecraft:magenta_shulker_box',
            'minecraft:light_blue_shulker_box',
            'minecraft:yellow_shulker_box',
            'minecraft:lime_shulker_box',
            'minecraft:pink_shulker_box',
            'minecraft:gray_shulker_box',
            'minecraft:light_gray_shulker_box',
            'minecraft:cyan_shulker_box',
            'minecraft:purple_shulker_box',
            'minecraft:blue_shulker_box',
            'minecraft:brown_shulker_box',
            'minecraft:green_shulker_box',
            'minecraft:red_shulker_box',
            'minecraft:black_shulker_box'
        ];
        // Choose a random shulker box color
        const randomType = shulkerBoxTypes[Math.floor(Math.random() * shulkerBoxTypes.length)];
        if (!randomType) {
            throw new Error('Failed to select random shulker box type');
        }
        const chosenShulkerBox = BlockTypes.get(randomType);
        if (!chosenShulkerBox) {
            throw new Error(`Invalid block type: ${randomType}`);
        }
        // Calculate alternate directions clockwise from preferred direction
        const directions = [
            preferredDirection,
            { x: -preferredDirection.z, y: 0, z: preferredDirection.x },
            { x: -preferredDirection.x, y: 0, z: -preferredDirection.z },
            { x: preferredDirection.z, y: 0, z: -preferredDirection.x }
        ];
        // Try each direction starting with the preferred one
        for (const direction of directions) {
            const spawnLocation = {
                x: Math.floor(player.location.x + direction.x * 2),
                y: Math.floor(player.location.y),
                z: Math.floor(player.location.z + direction.z * 2)
            };
            const block = player.dimension.getBlock(spawnLocation);
            if (block?.isAir || block?.isLiquid) {
                // Try to spawn a shulker box (using specified color)
                block.setType(chosenShulkerBox);
                const shulkerBox = player.dimension.getBlock(spawnLocation);
                if (shulkerBox) {
                    // Visual and sound effects
                    shulkerBox.dimension.spawnEntity('minecraft:fireworks_rocket', spawnLocation);
                    shulkerBox.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', spawnLocation);
                    shulkerBox.dimension.playSound('mob.shulker.open', spawnLocation);
                    // Fill shulker box with rare loot
                    player.runCommand(`/loot insert ${spawnLocation.x} ${spawnLocation.y} ${spawnLocation.z} loot "ptd/lm_hc/event51"`);
                    // Wait 1 tick then break the shulker box with effects
                    await system.waitTicks(1);
                    player.runCommand(`/setblock ${spawnLocation.x} ${spawnLocation.y} ${spawnLocation.z} air [] destroy`);
                    return;
                }
            }
        }
        console.warn('No suitable location found for spawning shulker box.');
        // Here we could add a fallback mechanism
    }
    catch (error) {
        console.warn(`Failed to execute event 51: ${error}`);
    }
    return;
}
