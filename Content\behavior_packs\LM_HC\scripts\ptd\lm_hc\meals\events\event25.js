import { getRandomLocation } from '../../utilities/vector3';
/**
 * Event 25: Golem Guardian
 * Spawns a mega iron golem
 * @param player The player who triggered the event
 */
export function event25(player) {
    try {
        const location = getRandomLocation(player.location, player.dimension, 5, 3, 1, true);
        if (location) {
            player.dimension.spawnEntity('ptd_lmhc:golem_guardian', location);
            player.dimension.spawnParticle('minecraft:cauldron_explosion_emitter', location);
            player.dimension.playSound('mob.endermen.portal', location);
        }
        else {
            console.warn('Could not find a safe location to spawn Golem Guardian.');
        }
    }
    catch (error) {
        console.warn(`Failed to execute event 25: ${error}`);
    }
    return;
}
