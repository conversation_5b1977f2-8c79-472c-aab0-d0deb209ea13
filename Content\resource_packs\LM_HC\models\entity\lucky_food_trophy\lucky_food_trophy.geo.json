{"format_version": "1.12.0", "minecraft:geometry": [{"description": {"identifier": "geometry.ptd_lmhc.lucky_food_trophy", "texture_width": 128, "texture_height": 128, "visible_bounds_width": 3, "visible_bounds_height": 5.5, "visible_bounds_offset": [0, 2.25, 0]}, "bones": [{"name": "root", "pivot": [0, 0, 0]}, {"name": "lucky_food_trophy", "parent": "root", "pivot": [0, 20, 0], "cubes": [{"origin": [-13, 0, -13], "size": [26, 2, 26], "uv": [0, 0]}, {"origin": [-12, 2, -12], "size": [24, 12, 24], "uv": [0, 28]}, {"origin": [-13, 14, -13], "size": [26, 2, 26], "uv": [0, 64]}]}, {"name": "golden_spoon", "parent": "lucky_food_trophy", "pivot": [0, 36, -12.1], "rotation": [0, 0, 45], "cubes": [{"origin": [-16.43503, 55.43503, -1.1], "size": [6, 6, 2], "uv": [104, 20]}, {"origin": [-14.43503, 37.43503, -1.1], "size": [2, 18, 2], "uv": [96, 44]}]}, {"name": "golden_fork", "parent": "lucky_food_trophy", "pivot": [0, 36, -12], "rotation": [0, 0, -45], "cubes": [{"origin": [10.78503, 56.43503, -1], "size": [1, 5, 2], "uv": [104, 47]}, {"origin": [15.08503, 56.43503, -1], "size": [1, 5, 2], "uv": [104, 47]}, {"origin": [12.18503, 56.43503, -1], "size": [1, 5, 2], "uv": [104, 47]}, {"origin": [13.68503, 56.43503, -1], "size": [1, 5, 2], "uv": [104, 47]}, {"origin": [10.93503, 55.43503, -1], "size": [5, 1, 2], "inflate": 0.2, "uv": [104, 44]}, {"origin": [12.43503, 37.43503, -1], "size": [2, 18, 2], "uv": [104, 0]}]}, {"name": "player", "parent": "root", "pivot": [0, 15, 0]}, {"name": "body_player", "parent": "player", "pivot": [0, 27, 0], "cubes": [{"origin": [-4, 27, -2], "size": [8, 12, 4], "uv": [32, 92]}]}, {"name": "head_player", "parent": "body_player", "pivot": [0, 39, 0], "rotation": [-22.5, 0, 0], "cubes": [{"origin": [-4, 39, -4], "size": [8, 8, 8], "uv": [0, 92]}]}, {"name": "left_arm_player", "parent": "body_player", "pivot": [6, 37, 0], "rotation": [180, 0, 22.5], "cubes": [{"origin": [4, 27, -2], "size": [4, 12, 4], "uv": [56, 92]}]}, {"name": "right_arm_player", "parent": "body_player", "pivot": [-6, 37, 0], "rotation": [180, 0, -22.5], "cubes": [{"origin": [-8, 27, -2], "size": [4, 12, 4], "uv": [72, 92]}]}, {"name": "left_leg_player", "parent": "player", "pivot": [2, 27, 0], "rotation": [22.5, 0, 0], "cubes": [{"origin": [0.25, 15, -2], "size": [4, 12, 4], "uv": [96, 28]}]}, {"name": "right_leg_player", "parent": "player", "pivot": [-2, 30, -4], "rotation": [22.5, 0, 0], "cubes": [{"origin": [-4.25, 18, -6], "size": [4, 12, 4], "uv": [88, 92]}]}]}]}