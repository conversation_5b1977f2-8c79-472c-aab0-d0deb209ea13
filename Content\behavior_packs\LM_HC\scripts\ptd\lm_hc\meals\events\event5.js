import { BlockTypes, ItemStack, system } from '@minecraft/server';
/**
 * Event 5. Dragon's Hoard: Creates a realistic pile of gold blocks, emerald blocks, and enchanted golden apples.
 * Materializes the treasure gradually in a heap-like formation with random placement.
 * Performs block checks before spawning to ensure proper placement.
 *
 * @param player - The player around whom the treasure hoard will materialize
 * @throws Will log a warning if the treasure spawning fails
 */
export function event5(player) {
    try {
        // Get block types upfront and validate
        const goldBlockType = BlockTypes.get('minecraft:gold_block');
        const emeraldBlockType = BlockTypes.get('minecraft:emerald_block');
        if (!goldBlockType || !emeraldBlockType) {
            throw new Error('Failed to get required block types');
        }
        // Error counter for tracking spawn failures
        let errorCounter = 0;
        const MAX_ERRORS = 5;
        const CONFIG = {
            BLOCKS: {
                GOLD: goldBlockType,
                EMERALD: emeraldBlockType
            },
            SPAWN_INTERVAL: 2, // Ticks between spawns (blocks and apples)
            APPLE_COUNT: 5, // Number of apples to spawn
            APPLE_VELOCITY: {
                // Velocity range for apple spawning
                X: [-0.3, 0.3],
                Y: [0.2, 0.4],
                Z: [-0.3, 0.3]
            }
        };
        // List of blocks that can be replaced
        const REPLACEABLE_BLOCKS = new Set([
            // Basic vegetation
            'minecraft:air',
            'minecraft:tallgrass',
            'minecraft:short_grass',
            'minecraft:grass',
            'minecraft:fern',
            'minecraft:deadbush',
            'minecraft:seagrass',
            'minecraft:tall_seagrass',
            'minecraft:moss_carpet',
            // Flowers (single block)
            'minecraft:red_flower',
            'minecraft:yellow_flower',
            'minecraft:wither_rose',
            'minecraft:cornflower',
            'minecraft:lily_of_the_valley',
            'minecraft:sweet_berry_bush',
            'minecraft:tulip',
            'minecraft:allium',
            'minecraft:azure_bluet',
            'minecraft:blue_orchid',
            'minecraft:dandelion',
            'minecraft:oxeye_daisy',
            'minecraft:poppy',
            // Tall flowers
            'minecraft:double_plant',
            'minecraft:lilac',
            'minecraft:peony',
            'minecraft:rose_bush',
            'minecraft:sunflower',
            'minecraft:tall_grass',
            'minecraft:large_fern',
            // Vines and climbing plants
            'minecraft:vine',
            'minecraft:twisting_vines',
            'minecraft:weeping_vines',
            'minecraft:cave_vines',
            // Cave and water vegetation
            'minecraft:hanging_roots',
            'minecraft:big_dripleaf',
            'minecraft:small_dripleaf',
            'minecraft:waterlily',
            'minecraft:kelp',
            'minecraft:bamboo',
            'minecraft:bamboo_sapling',
            'minecraft:cactus',
            // Coral
            'minecraft:coral',
            'minecraft:tube_coral',
            'minecraft:brain_coral',
            'minecraft:bubble_coral',
            'minecraft:fire_coral',
            'minecraft:horn_coral',
            // Leaves
            'minecraft:azalea_leaves',
            'minecraft:flowering_azalea_leaves',
            'minecraft:leaves',
            'minecraft:leaves2',
            'minecraft:mangrove_leaves',
            'minecraft:cherry_leaves',
            // Crops
            'minecraft:wheat',
            'minecraft:carrots',
            'minecraft:potatoes',
            'minecraft:beetroot',
            'minecraft:sweet_berries',
            'minecraft:torchflower',
            'minecraft:pitcher_crop',
            'minecraft:pitcher_plant',
            'minecraft:melon_stem',
            'minecraft:pumpkin_stem',
            'minecraft:nether_wart',
            'minecraft:cocoa',
            'minecraft:cave_vines_head_with_berries',
            'minecraft:cave_vines_body_with_berries'
        ]);
        // Calculate initial position 3 blocks in front of player
        const headLoc = player.getHeadLocation();
        const viewDir = player.getViewDirection();
        const initialPos = {
            x: Math.floor(headLoc.x + viewDir.x * 3),
            y: Math.floor(headLoc.y - 1), // Start one block below eye level
            z: Math.floor(headLoc.z + viewDir.z * 3)
        };
        const pilePattern = [
            // Base layer (7x7)
            Array.from({ length: 49 }, (_, i) => ({
                x: (i % 7) - 3,
                y: 0,
                z: Math.floor(i / 7) - 3
            })),
            // Second layer (5x5)
            Array.from({ length: 25 }, (_, i) => ({
                x: (i % 5) - 2,
                y: 1,
                z: Math.floor(i / 5) - 2
            })),
            // Third layer (3x3)
            Array.from({ length: 9 }, (_, i) => ({
                x: (i % 3) - 1,
                y: 2,
                z: Math.floor(i / 3) - 1
            })),
            // Top layer (1x1)
            [{ x: 0, y: 3, z: 0 }]
        ];
        // Convert layers to randomized arrays
        const randomizedLayers = pilePattern.map((layer) => layer.sort(() => Math.random() - 0.5));
        let currentLayer = 0;
        let positionInLayer = 0;
        // Function to spawn apples after blocks are placed
        const spawnApples = () => {
            const enchantedApple = new ItemStack('minecraft:enchanted_golden_apple');
            let applesSpawned = 0;
            // Define fixed positions for apple spawns
            const TOP_POSITIONS = [
                { x: 0, y: 4, z: 0 }, // Center peak
                { x: -1, y: 3, z: -1 }, // Corners of top layer
                { x: 1, y: 3, z: -1 },
                { x: -1, y: 3, z: 1 },
                { x: 1, y: 3, z: 1 }
            ];
            const getRandomVelocity = () => ({
                x: CONFIG.APPLE_VELOCITY.X[0] +
                    Math.random() * (CONFIG.APPLE_VELOCITY.X[1] - CONFIG.APPLE_VELOCITY.X[0]),
                y: CONFIG.APPLE_VELOCITY.Y[0] +
                    Math.random() * (CONFIG.APPLE_VELOCITY.Y[1] - CONFIG.APPLE_VELOCITY.Y[0]),
                z: CONFIG.APPLE_VELOCITY.Z[0] +
                    Math.random() * (CONFIG.APPLE_VELOCITY.Z[1] - CONFIG.APPLE_VELOCITY.Z[0])
            });
            const appleRunId = system.runInterval(() => {
                try {
                    // Check if max errors reached
                    if (errorCounter >= MAX_ERRORS) {
                        console.warn(`Dragon's Hoard event: Max spawn errors (${MAX_ERRORS}) reached. Terminating event.`);
                        system.clearRun(appleRunId);
                        return;
                    }
                    if (applesSpawned >= CONFIG.APPLE_COUNT) {
                        system.clearRun(appleRunId);
                        return;
                    }
                    const position = TOP_POSITIONS[applesSpawned % TOP_POSITIONS.length];
                    if (position) {
                        const spawnLoc = {
                            x: initialPos.x + position.x + 0.5,
                            y: initialPos.y + position.y,
                            z: initialPos.z + position.z + 0.5
                        };
                        // Spawn the apple with velocity and effects
                        const velocity = getRandomVelocity();
                        const apple = player.dimension.spawnItem(enchantedApple, spawnLoc);
                        // Verify apple spawned successfully
                        if (!apple) {
                            errorCounter++;
                            console.warn(`Dragon's Hoard event: Failed to spawn apple. Error count: ${errorCounter}/${MAX_ERRORS}`);
                            return;
                        }
                        apple.applyImpulse(velocity);
                        player.dimension.spawnParticle('minecraft:totem_particle', spawnLoc);
                        player.runCommand(`playsound random.pop @s ${spawnLoc.x} ${spawnLoc.y} ${spawnLoc.z} 1 1`);
                    }
                    applesSpawned++;
                }
                catch (error) {
                    errorCounter++;
                    console.warn(`Dragon's Hoard event: Error spawning apple: ${error}. Error count: ${errorCounter}/${MAX_ERRORS}`);
                    if (errorCounter >= MAX_ERRORS) {
                        console.warn(`Dragon's Hoard event: Max spawn errors (${MAX_ERRORS}) reached. Terminating event.`);
                        system.clearRun(appleRunId);
                    }
                }
            }, CONFIG.SPAWN_INTERVAL);
        };
        // First phase: Block placement
        const blockRunId = system.runInterval(() => {
            try {
                // Check if max errors reached
                if (errorCounter >= MAX_ERRORS) {
                    console.warn(`Dragon's Hoard event: Max spawn errors (${MAX_ERRORS}) reached. Terminating event.`);
                    system.clearRun(blockRunId);
                    return;
                }
                if (currentLayer >= randomizedLayers.length) {
                    system.clearRun(blockRunId);
                    // Start apple spawning phase
                    spawnApples();
                    return;
                }
                const layer = randomizedLayers[currentLayer];
                if (!layer || positionInLayer >= layer.length) {
                    currentLayer++;
                    positionInLayer = 0;
                    return;
                }
                const offset = layer[positionInLayer];
                if (!offset) {
                    positionInLayer++;
                    return;
                }
                const spawnLoc = {
                    x: initialPos.x + offset.x,
                    y: initialPos.y + offset.y,
                    z: initialPos.z + offset.z
                };
                const block = player.dimension.getBlock(spawnLoc);
                if (block && block.typeId && REPLACEABLE_BLOCKS.has(block.typeId)) {
                    // Random 50/50 chance between gold and emerald
                    const blockType = Math.random() < 0.5 ? CONFIG.BLOCKS.GOLD : CONFIG.BLOCKS.EMERALD;
                    block.setType(blockType);
                    // Spawn particle effect and play sound for materialization
                    player.dimension.spawnParticle('minecraft:dragon_breath_trail', spawnLoc);
                    player.runCommand(`playsound break.iron @s ${spawnLoc.x} ${spawnLoc.y} ${spawnLoc.z} 1 1`);
                }
                positionInLayer++;
            }
            catch (error) {
                errorCounter++;
                console.warn(`Dragon's Hoard event: Error placing blocks: ${error}. Error count: ${errorCounter}/${MAX_ERRORS}`);
                if (errorCounter >= MAX_ERRORS) {
                    console.warn(`Dragon's Hoard event: Max spawn errors (${MAX_ERRORS}) reached. Terminating event.`);
                    system.clearRun(blockRunId);
                }
            }
        }, CONFIG.SPAWN_INTERVAL);
    }
    catch (error) {
        console.warn(`Failed to execute Dragon's Hoard event: ${error}`);
    }
    return;
}
