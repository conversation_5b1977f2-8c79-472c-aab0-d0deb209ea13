import { system } from '@minecraft/server';
import { getDistance } from '../vector3';
import { excludedEntitiesPlayerForAll } from '../entityQueries';
// Define safe blocks to swap between
const SWAP_BLOCKS = [
    'minecraft:stone',
    'minecraft:dirt',
    'minecraft:grass',
    'minecraft:cobblestone',
    'minecraft:oak_planks',
    'minecraft:birch_planks',
    'minecraft:spruce_planks',
    'minecraft:sandstone',
    'minecraft:gravel',
    'minecraft:sand',
    'minecraft:granite',
    'minecraft:andesite',
    'minecraft:diorite',
    // Additional blocks
    'minecraft:deepslate',
    'minecraft:tuff',
    'minecraft:calcite',
    'minecraft:acacia_planks',
    'minecraft:jungle_planks',
    'minecraft:dark_oak_planks',
    'minecraft:mangrove_planks',
    'minecraft:cherry_planks',
    'minecraft:crimson_planks',
    'minecraft:warped_planks',
    'minecraft:stone_bricks',
    'minecraft:mossy_cobblestone',
    'minecraft:prismarine',
    // New additions
    'minecraft:copper_block',
    'minecraft:exposed_copper',
    'minecraft:weathered_copper',
    'minecraft:oxidized_copper',
    'minecraft:cut_copper',
    'minecraft:amethyst_block',
    'minecraft:blackstone',
    'minecraft:gilded_blackstone',
    'minecraft:polished_blackstone',
    'minecraft:polished_granite',
    'minecraft:polished_andesite',
    'minecraft:polished_diorite',
    'minecraft:polished_deepslate',
    'minecraft:deepslate_bricks',
    'minecraft:deepslate_tiles',
    'minecraft:mud_bricks',
    'minecraft:packed_mud',
    'minecraft:smooth_stone',
    'minecraft:smooth_sandstone',
    'minecraft:red_sandstone',
    'minecraft:smooth_red_sandstone'
];
// Define safe entities to swap between
const SWAP_ENTITIES = [
    // Passive mobs
    'minecraft:chicken',
    'minecraft:cow',
    'minecraft:sheep',
    'minecraft:pig',
    'minecraft:rabbit',
    'minecraft:cat',
    'minecraft:wolf',
    'minecraft:llama',
    'minecraft:donkey',
    'minecraft:horse',
    'minecraft:mooshroom',
    'minecraft:goat',
    'minecraft:fox',
    'minecraft:panda',
    'minecraft:parrot',
    'minecraft:turtle',
    'minecraft:dolphin',
    'minecraft:axolotl',
    'minecraft:bat',
    'minecraft:camel',
    'minecraft:frog',
    'minecraft:sniffer',
    'minecraft:polar_bear',
    'minecraft:ocelot',
    // Neutral mobs
    'minecraft:bee',
    'minecraft:iron_golem',
    'minecraft:snow_golem',
    'minecraft:trader_llama',
    'minecraft:wandering_trader',
    'minecraft:villager',
    // Hostile mobs
    'minecraft:zombie',
    'minecraft:skeleton',
    'minecraft:creeper',
    'minecraft:spider',
    'minecraft:cave_spider',
    'minecraft:enderman',
    'minecraft:witch',
    'minecraft:slime',
    'minecraft:magma_cube',
    'minecraft:blaze',
    'minecraft:ghast',
    'minecraft:phantom',
    'minecraft:drowned',
    'minecraft:guardian',
    'minecraft:elder_guardian',
    'minecraft:pillager',
    'minecraft:vindicator',
    'minecraft:evocation_illager',
    'minecraft:ravager',
    'minecraft:vex',
    'minecraft:stray',
    'minecraft:husk',
    'minecraft:zombie_villager',
    'minecraft:piglin',
    'minecraft:piglin_brute',
    'minecraft:zombie_pigman',
    'minecraft:hoglin',
    'minecraft:zoglin',
    'minecraft:shulker',
    'minecraft:silverfish',
    'minecraft:endermite',
    'minecraft:strider',
    'minecraft:allay'
];
// Define sound mappings for block categories
const BLOCK_SOUNDS = {
    stone: ['dig.stone', 'hit.stone', 'land.stone'],
    wood: ['dig.wood', 'hit.wood', 'land.wood'],
    dirt: ['dig.gravel', 'hit.gravel', 'land.gravel'],
    sand: ['dig.sand', 'hit.sand', 'land.sand'],
    grass: ['dig.grass', 'hit.grass', 'land.grass'],
    metal: ['mob.armor_stand.hit', 'block.copper.hit'],
    crystal: ['break.amethyst_block', 'hit.amethyst_block'],
    deepslate: ['dig.deepslate', 'hit.deepslate', 'break.deepslate'],
    nether: ['dig.netherrack', 'hit.netherrack', 'break.nether_brick']
};
// Define sound mappings for entity categories
const ENTITY_SOUNDS = {
    passive: {
        ambient: [
            'mob.chicken.say',
            'mob.cow.say',
            'mob.pig.say',
            'mob.sheep.say',
            'mob.rabbit.hurt',
            'mob.horse.angry',
            'mob.llama.angry'
        ],
        death: [
            'mob.chicken.hurt',
            'mob.cow.hurt',
            'mob.pig.death',
            'mob.sheep.say',
            'mob.rabbit.death',
            'mob.horse.death',
            'mob.llama.death'
        ]
    },
    neutral: {
        ambient: [
            'mob.iron_golem.hurt',
            'mob.polar_bear.warning',
            'mob.wolf.growl',
            'mob.bee.loop',
            'mob.dolphin.idle'
        ],
        death: [
            'mob.iron_golem.death',
            'mob.polar_bear.death',
            'mob.wolf.death',
            'mob.bee.death',
            'mob.dolphin.death'
        ]
    },
    hostile: {
        ambient: [
            'mob.creeper.say',
            'mob.zombie.say',
            'mob.skeleton.say',
            'mob.spider.say',
            'mob.blaze.breathe',
            'mob.ghast.scream'
        ],
        death: [
            'mob.creeper.death',
            'mob.zombie.death',
            'mob.skeleton.death',
            'mob.spider.death',
            'mob.blaze.death',
            'mob.ghast.death'
        ]
    }
};
/**
 * Gets appropriate sound for a block based on its type
 * @param blockId The block's identifier
 * @returns Sound string to play
 */
function getBlockSwapSound(blockId) {
    if (blockId.includes('stone') || blockId.includes('deepslate'))
        return (BLOCK_SOUNDS.deepslate[Math.floor(Math.random() * BLOCK_SOUNDS.deepslate.length)] ??
            BLOCK_SOUNDS.deepslate[0]);
    if (blockId.includes('wood') || blockId.includes('planks'))
        return BLOCK_SOUNDS.wood[Math.floor(Math.random() * 3)] ?? BLOCK_SOUNDS.wood[0];
    if (blockId.includes('dirt') || blockId.includes('gravel'))
        return BLOCK_SOUNDS.dirt[Math.floor(Math.random() * 3)] ?? BLOCK_SOUNDS.dirt[0];
    if (blockId.includes('sand'))
        return BLOCK_SOUNDS.sand[Math.floor(Math.random() * 3)] ?? BLOCK_SOUNDS.sand[0];
    if (blockId.includes('grass'))
        return BLOCK_SOUNDS.grass[Math.floor(Math.random() * 3)] ?? BLOCK_SOUNDS.grass[0];
    if (blockId.includes('copper') || blockId.includes('iron'))
        return BLOCK_SOUNDS.metal[Math.floor(Math.random() * 2)] ?? BLOCK_SOUNDS.metal[0];
    if (blockId.includes('amethyst'))
        return BLOCK_SOUNDS.crystal[Math.floor(Math.random() * 2)] ?? BLOCK_SOUNDS.crystal[0];
    if (blockId.includes('nether'))
        return BLOCK_SOUNDS.nether[Math.floor(Math.random() * 3)] ?? BLOCK_SOUNDS.nether[0];
    return BLOCK_SOUNDS.stone[Math.floor(Math.random() * 3)] ?? BLOCK_SOUNDS.stone[0]; // Default to stone sounds
}
/**
 * Gets appropriate sound for an entity based on its type
 * @param entityId The entity's identifier
 * @returns Sound string to play
 */
function getEntitySwapSound(entityId) {
    const isPassive = entityId.match(/(chicken|cow|pig|sheep|rabbit|horse|llama|mooshroom|goat)/i);
    const isNeutral = entityId.match(/(iron_golem|polar_bear|wolf|bee|dolphin)/i);
    const isHostile = entityId.match(/(creeper|zombie|skeleton|spider|blaze|ghast)/i);
    if (isPassive) {
        return (ENTITY_SOUNDS.passive[Math.random() > 0.5 ? 'ambient' : 'death'][Math.floor(Math.random() * ENTITY_SOUNDS.passive.ambient.length)] ?? ENTITY_SOUNDS.passive.ambient[0]);
    }
    if (isNeutral) {
        return (ENTITY_SOUNDS.neutral[Math.random() > 0.5 ? 'ambient' : 'death'][Math.floor(Math.random() * ENTITY_SOUNDS.neutral.ambient.length)] ?? ENTITY_SOUNDS.neutral.ambient[0]);
    }
    if (isHostile) {
        return (ENTITY_SOUNDS.hostile[Math.random() > 0.5 ? 'ambient' : 'death'][Math.floor(Math.random() * ENTITY_SOUNDS.hostile.ambient.length)] ?? ENTITY_SOUNDS.hostile.ambient[0]);
    }
    return (ENTITY_SOUNDS.passive.ambient[Math.floor(Math.random() * ENTITY_SOUNDS.passive.ambient.length)] ??
        ENTITY_SOUNDS.passive.ambient[0]);
}
/**
 * Creates particles along a line between two points using calculated distance steps
 * @param dimension The dimension to spawn particles in
 * @param start Starting point
 * @param end End point
 * @param step Distance between particles
 */
function createParticleLine(dimension, start, end, step = 0.5) {
    const distance = getDistance(start, end);
    const steps = Math.floor(distance / step);
    const dx = (end.x - start.x) / steps;
    const dy = (end.y - start.y) / steps;
    const dz = (end.z - start.z) / steps;
    for (let i = 0; i <= steps; i++) {
        dimension.spawnParticle('minecraft:redstone_wire_dust_particle', {
            x: start.x + dx * i,
            y: start.y + dy * i,
            z: start.z + dz * i
        });
    }
}
/**
 * Eyes of Madness: Takes the block player is looking at and randomly
 * swaps both blocks in a 3x3 area and entities within a 5-block radius around it
 * @param player The player who triggered the event
 */
export function event40(player) {
    const CONFIG = {
        DURATION: 100,
        TICK_INTERVAL: 1,
        ENTITY_RADIUS: 5
    };
    try {
        let count = 0;
        const runId = system.runInterval(async () => {
            if (count >= CONFIG.DURATION) {
                system.clearRun(runId);
                return;
            }
            const startPoint = player.getHeadLocation();
            const direction = player.getViewDirection();
            const blockRaycast = player.dimension.getBlockFromRay(startPoint, direction, { maxDistance: 32 });
            const block = blockRaycast?.block;
            if (!block) {
                count++;
                return;
            }
            // Create particle line
            const targetPoint = {
                x: block.location.x + 0.5,
                y: block.location.y + 0.5,
                z: block.location.z + 0.5
            };
            createParticleLine(player.dimension, startPoint, targetPoint);
            // Store center position
            const centerX = Math.floor(block.location.x);
            const centerY = Math.floor(block.location.y);
            const centerZ = Math.floor(block.location.z);
            // Swap blocks in 3x3 area
            for (let x = -1; x <= 1; x++) {
                for (let z = -1; z <= 1; z++) {
                    const currentBlock = block.dimension.getBlock({
                        x: centerX + x,
                        y: centerY,
                        z: centerZ + z
                    });
                    if (currentBlock && !currentBlock.isLiquid && !currentBlock.isAir) {
                        try {
                            // Get random block type from our safe list
                            const randomBlockType = SWAP_BLOCKS[Math.floor(Math.random() * SWAP_BLOCKS.length)];
                            if (randomBlockType) {
                                currentBlock.setType(randomBlockType);
                                // Play block swap sound
                                const blockSwapSound = getBlockSwapSound(randomBlockType);
                                player.dimension.playSound(blockSwapSound, currentBlock.location);
                            }
                        }
                        catch (error) {
                            console.warn(`Failed to swap block at ${x},${z}: ${error}`);
                        }
                    }
                }
            }
            // Get and swap nearby entities
            const nearbyEntities = block.dimension.getEntities({
                location: targetPoint,
                maxDistance: CONFIG.ENTITY_RADIUS,
                ...excludedEntitiesPlayerForAll
            });
            for (const entity of nearbyEntities) {
                // Skip players and non-living entities
                if (entity.typeId === 'minecraft:player' || !entity)
                    continue;
                let randomEntityType;
                try {
                    // Get random entity type from our safe list
                    randomEntityType = SWAP_ENTITIES[Math.floor(Math.random() * SWAP_ENTITIES.length)];
                    if (randomEntityType) {
                        const pos = entity.location;
                        // Remove old entity and spawn new one
                        entity.remove();
                        await system.waitTicks(1);
                        block.dimension.spawnEntity(randomEntityType, pos);
                        // Play entity swap sound
                        const entitySwapSound = getEntitySwapSound(randomEntityType);
                        player.dimension.playSound(entitySwapSound, pos);
                    }
                }
                catch (error) {
                    console.warn(`Failed to swap entity from ${entity.typeId} to ${randomEntityType}: ${error}`);
                }
            }
            count++;
        }, CONFIG.TICK_INTERVAL);
    }
    catch (error) {
        console.warn(`Failed to execute Eyes of Madness event: ${error}`);
    }
    return;
}
