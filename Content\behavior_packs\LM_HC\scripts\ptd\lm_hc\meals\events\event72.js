/**
 * Constants for the "Wither's Breath" event
 */
const EFFECT_DURATION = 15 * 20; // 15 seconds in ticks
const EFFECT_AMPLIFIER = 1; // Wither effect level II
/**
 * Event 72: Wither's Breath - Player is affected by <PERSON><PERSON> for 15 seconds
 * Apply the Wither effect to the player, causing gradual damage over time
 *
 * @param player - The player who triggered the event
 */
export function event72(player) {
    try {
        // Apply wither effect to the player
        player.addEffect('wither', EFFECT_DURATION, {
            amplifier: EFFECT_AMPLIFIER,
            showParticles: true
        });
        // Add visual and sound effects
        const dimension = player.dimension;
        const playerPos = player.location;
        // Play wither sound effect
        dimension.playSound('mob.wither.spawn', playerPos, {
            volume: 0.5,
            pitch: 1.2
        });
    }
    catch (error) {
        console.warn(`Failed to execute event 72: ${error}`);
    }
    return;
}
