/**
 * Constants for the "Treasure King" event
 */
const EFFECT_DURATION = 45 * 20; // 45 seconds in ticks
const SLOWNESS_AMPLIFIER = 1; // Slowness level II
const MINING_FATIGUE_AMPLIFIER = 1; // Mining Fatigue level II
/**
 * Event 75: Treasure King
 * Gives the player enchanted golden armor, but applies slowness and mining fatigue for 45 seconds
 * @param player - The player who triggered the event
 */
export function event75(player) {
    try {
        const dimension = player.dimension;
        const spawnLocation = player.location;
        // Spawn the enchanted golden armor set using loot table
        dimension.runCommand(`loot spawn ${spawnLocation.x} ${spawnLocation.y} ${spawnLocation.z} loot "ptd/lm_hc/event75"`);
        // Apply negative effects
        player.addEffect('slowness', EFFECT_DURATION, {
            amplifier: SLOWNESS_AMPLIFIER,
            showParticles: true
        });
        player.addEffect('mining_fatigue', EFFECT_DURATION, {
            amplifier: MINING_FATIGUE_AMPLIFIER,
            showParticles: true
        });
        // Add particles and sound effects
        dimension.playSound('random.totem', spawnLocation, { volume: 0.4 });
        dimension.playSound('random.levelup', spawnLocation, { volume: 0.5, pitch: 0.8 });
        for (let i = 0; i < 15; i++) {
            dimension.spawnParticle('minecraft:totem_particle', spawnLocation);
        }
    }
    catch (error) {
        console.warn(`Failed to execute Treasure King event: ${error}`);
    }
    return;
}
