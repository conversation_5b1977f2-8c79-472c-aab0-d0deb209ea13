import { BlockPermutation } from '@minecraft/server';
import { PROTECTED_BLOCKS } from '../../utilities/constants/protectedBlocks';
/**
 * Event 68: Cobweb Cage - Surrounds the player in a 7x7x7 cobweb cage
 * Creates a 7x7x7 structure of cobwebs around the player, completely filling the area
 *
 * @param player - The player who triggered the event
 */
export function event68(player) {
    try {
        const dimension = player.dimension;
        const playerPos = {
            x: Math.floor(player.location.x),
            y: Math.floor(player.location.y),
            z: Math.floor(player.location.z)
        };
        // Create cobweb permutation
        const cobwebPermutation = BlockPermutation.resolve('minecraft:web');
        // Size of the cobweb cage (7x7x7)
        const cageSize = 3; // 3 blocks in each direction from player
        // Sound and visual effect at start
        dimension.playSound('block.web.step', playerPos);
        dimension.spawnParticle('minecraft:large_explosion', playerPos);
        // Create the cobweb cage immediately
        for (let x = -cageSize; x <= cageSize; x++) {
            for (let y = -cageSize; y <= cageSize; y++) {
                for (let z = -cageSize; z <= cageSize; z++) {
                    // Process all blocks in the area including the center
                    const blockPos = {
                        x: playerPos.x + x,
                        y: playerPos.y + y,
                        z: playerPos.z + z
                    };
                    // Get the block at this position
                    const block = dimension.getBlock(blockPos);
                    // Skip if the block doesn't exist
                    if (!block) {
                        continue;
                    }
                    // Check if this is a protected block type that shouldn't be replaced
                    if (PROTECTED_BLOCKS.has(block.type.id)) {
                        continue;
                    }
                    // Check if the block is not air or is liquid
                    if (!block.isAir || block.isLiquid) {
                        continue;
                    }
                    // Place cobweb
                    dimension.getBlock(blockPos)?.setPermutation(cobwebPermutation);
                }
            }
        }
    }
    catch (error) {
        console.warn(`Failed to execute event 68: ${error}`);
    }
    return;
}
